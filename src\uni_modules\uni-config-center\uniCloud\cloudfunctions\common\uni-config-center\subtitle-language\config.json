{"languageMap": {"zh": "中文", "en": "英文", "ja": "日文", "ko": "韩文", "fr": "法文", "de": "德文", "es": "西班牙文", "it": "意大利文", "pt": "葡萄牙文", "ru": "俄文", "ar": "阿拉伯文", "hi": "印地文", "th": "泰文", "vi": "越南文", "tr": "土耳其文", "pl": "波兰文", "nl": "荷兰文", "sv": "瑞典文", "da": "丹麦文", "no": "挪威文", "fi": "芬兰文"}, "languageIdentifierMap": {"Chinese": "zh", "chinese": "zh", "zh-cn": "zh", "zh-tw": "zh-tw", "Simplified Chinese": "zh", "Traditional Chinese": "zh-tw", "English": "en", "english": "en", "Japanese": "ja", "japanese": "ja", "Korean": "ko", "korean": "ko", "French": "fr", "french": "fr", "German": "de", "german": "de", "Spanish": "es", "spanish": "es", "Italian": "it", "italian": "it", "Portuguese": "pt", "portuguese": "pt", "Russian": "ru", "russian": "ru", "Arabic": "ar", "arabic": "ar", "Hindi": "hi", "hindi": "hi", "Thai": "th", "thai": "th", "Vietnamese": "vi", "vietnamese": "vi"}, "languageFonts": {"zh": "Microsoft YaHei", "zh-cn": "Microsoft YaHei", "zh-tw": "Microsoft JhengHei", "ja": "MS Gothic", "ko": "Malgun Gothic", "en": "<PERSON><PERSON>", "default": "<PERSON><PERSON>"}, "languageEncodings": {"zh": 134, "zh-cn": 134, "zh-tw": 136, "en": 0, "ja": 128, "ko": 129, "ar": 178, "hi": 0, "th": 222, "vi": 163, "ru": 204}, "wrapConfig": {"cjkLanguages": ["zh", "zh-cn", "zh-tw", "ja", "ko"], "intelligentWrapLanguages": ["zh", "zh-cn", "zh-tw"], "characterWidthFactors": {"zh": 0.8, "zh-cn": 0.8, "zh-tw": 0.8, "ja": 0.8, "ko": 0.8, "en": 0.55, "fr": 0.55, "de": 0.55, "es": 0.55, "it": 0.55, "pt": 0.55, "ru": 0.6, "ar": 0.7, "hi": 0.7, "th": 0.7, "vi": 0.55, "default": 0.6}, "maxLineSettings": {"preferredMaxLines": 2, "absoluteMaxLines": 3, "minCharsPerLine": 10, "safeWidthRatio": 0.95, "wrapThreshold": 1.15}, "breakRules": {"cjk": {"canBreakAnywhere": true, "avoidBreakAfter": ["（", "【", "《", "\"", "'"], "avoidBreakBefore": ["）", "】", "》", "\"", "'", "，", "。", "！", "？", "；", "："]}, "western": {"canBreakAnywhere": false, "breakOnWhitespace": true, "breakOnHyphen": true, "avoidBreakAfter": ["(", "[", "{", "\"", "'"], "avoidBreakBefore": [")", "]", "}", "\"", "'", ",", ".", "!", "?", ";", ":"]}}}, "punctuationRules": {"cjk": {"avoidBreakAfter": ["、", "。", "，", "：", "；", "“", "‘", "（", "【", "《", "〈"], "avoidBreakBefore": ["。", "！", "？", "，", "；", "：", "”", "’", "）", "】", "》", "〉", "…"], "preferBreakAfter": ["。", "！", "？", "；"]}, "western": {"avoidBreakAfter": ["a", "an", "the", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with"], "avoidBreakBefore": [".", ",", "!", "?", ";", ":", ")", "]", "}"]}}, "assStyleConfig": {"baseStyle": {"fontName": "<PERSON><PERSON>", "primaryColor": "&H0000FFFF", "secondaryColor": "&H0000FFFF", "outlineColor": "&H00000000", "backColor": "&H00000000", "bold": 0, "italic": 0, "underline": 0, "strikeOut": 0, "scaleX": 100, "scaleY": 100, "spacing": 0, "angle": 0, "borderStyle": 1, "outline": 1, "shadow": 1, "alignment": 2}, "_deprecated_old_config": {"_note": "以下配置已废弃，现在使用基于业界标准的全新字体大小计算逻辑", "_new_algorithm": "基于屏幕高度百分比的科学计算方法", "_reference_standards": ["广播电视字幕显示标准", "主流视频播放器(VLC, mpv)缩放算法", "ASS字幕格式官方规范", "流媒体平台最佳实践"]}, "scriptInfo": {"title": "Video Translation Subtitle", "scriptType": "v4.00+", "wrapStyle": 1, "scaledBorderAndShadow": "yes", "autoAdaptive": false}, "languageWrapStyles": {"zh": 0, "zh-cn": 0, "zh-tw": 0, "ja": 0, "ko": 0, "en": 1, "fr": 1, "de": 1, "es": 1, "it": 1, "pt": 1, "ru": 1, "ar": 0, "hi": 0, "th": 0, "vi": 0, "default": 0}}}